<?php
header("Content-Type:text/html;charset=UTF-8");
ini_set('date.timezone','Asia/Shanghai');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die('此接口仅支持POST请求，请使用admin_attachment.php管理界面');
}

$con = mysql_connect("hdm721877570.my3w.com","hdm721877570","Hanpan110119");
if (!$con)
  {
  die('Could not connect: ' . mysql_error());
  }

mysql_query('SET NAMES UTF8');
mysql_select_db("hdm721877570_db", $con);
$content = file_get_contents('php://input');
$post    = json_decode($content, true);
$code = $post["code"];
$attachment = $post["attachment"];

// 验证必要参数
if (empty($code) || empty($attachment)) {
    die('Error: 报告编号和附件地址不能为空');
}

// 更新指定报告编号的attachment字段
$sql="UPDATE report SET attachment = '$attachment' WHERE project_code = '$code'";

if (!mysql_query($sql,$con))
  {
  die('Error: ' . mysql_error());
  }

// 检查是否有记录被更新
$affected_rows = mysql_affected_rows($con);
if ($affected_rows > 0) {
    echo 1;
} else {
    echo 'Error: 未找到指定的报告编号';
}

mysql_close($con);
?>

<?php
header("Content-Type:text/html;charset=UTF-8");   
ini_set('date.timezone','Asia/Shanghai');
 
$con = mysql_connect("hdm721877570.my3w.com","hdm721877570","Hanpan110119");
if (!$con)
  {
  die('Could not connect: ' . mysql_error());
  }
 
mysql_query('SET NAMES UTF8');
mysql_select_db("hdm721877570_db", $con);
$content = file_get_contents('php://input');
$post    = json_decode($content, true);
$code = $post["code"];
$attachment = $post["attachment"];

// 更新指定报告编号的attachment字段
$sql="UPDATE report SET attachment = '$attachment' WHERE project_code = '$code'";

if (!mysql_query($sql,$con))
  {
  die('Error: ' . mysql_error());
  }
echo 1;

mysql_close($con);
?>

<?php
header("Content-Type:text/html;charset=UTF-8");   
ini_set('date.timezone','Asia/Shanghai');
 
$con = mysql_connect("hdm721877570.my3w.com","hdm721877570","Hanpan110119");
if (!$con)
  {
  die('Could not connect: ' . mysql_error());
  }
 
mysql_query('SET NAMES UTF8');
mysql_select_db("hdm721877570_db", $con);

echo "<h2>数据库表结构检查</h2>";

// 显示report表的所有字段
$sql = "DESCRIBE report";
$result = mysql_query($sql, $con);

if ($result) {
    echo "<h3>report表的字段列表：</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th><th>额外</th></tr>";
    
    while ($row = mysql_fetch_array($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 检查attachment字段
    $check_sql = "SHOW COLUMNS FROM report LIKE 'attachment'";
    $check_result = mysql_query($check_sql, $con);
    
    if (mysql_num_rows($check_result) > 0) {
        echo "<p style='color: green;'><strong>✓ attachment字段已存在</strong></p>";
    } else {
        echo "<p style='color: red;'><strong>✗ attachment字段不存在</strong></p>";
        echo "<p><a href='add_attachment_field.php'>点击这里添加attachment字段</a></p>";
    }
    
} else {
    echo "查询失败: " . mysql_error();
}

mysql_close($con);
?>

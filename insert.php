<?php
header("Content-Type:text/html;charset=UTF-8");
ini_set('date.timezone','Asia/Shanghai');

$con = mysql_connect("hdm721877570.my3w.com","hdm721877570","Hanpan110119");
if (!$con)
  {
  die('Could not connect: ' . mysql_error());
  }

mysql_query('SET NAMES UTF8');
mysql_select_db("hdm721877570_db", $con);
$content = file_get_contents('php://input');
$post    = json_decode($content, true);
$code = $post["code"];
$projectName = $post["projectName"];
$unit = $post["unit"];
$company = $post["company"];
$item = $post["item"];
$checkingOfType = $post["checkingOfType"];
$address = $post["address"];
$date = $post["date"];
$temperature = $post["temperature"];
$humidity = $post["humidity"];
$status = $post["status"];
$conclusion = $post["conclusion"];
$attachment = $post["attachment"];


$sql="INSERT INTO report (project_code, project_name,model,company,item,type,address,check_date,temperature,humidity,status,result,attachment)
VALUES
('$code','$projectName','$unit','$company','$item','$checkingOfType','$address','$date','$temperature','$humidity','$status','$conclusion','$attachment');";

if (!mysql_query($sql,$con))
  {
  die('Error: ' . mysql_error());
  }
echo 1;



?>
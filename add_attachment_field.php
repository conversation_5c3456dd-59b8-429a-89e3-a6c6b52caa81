<?php
header("Content-Type:text/html;charset=UTF-8");   
ini_set('date.timezone','Asia/Shanghai');
 
$con = mysql_connect("hdm721877570.my3w.com","hdm721877570","Hanpan110119");
if (!$con)
  {
  die('Could not connect: ' . mysql_error());
  }
 
mysql_query('SET NAMES UTF8');
mysql_select_db("hdm721877570_db", $con);

// 检查attachment字段是否已存在
$check_sql = "SHOW COLUMNS FROM report LIKE 'attachment'";
$result = mysql_query($check_sql, $con);

if (mysql_num_rows($result) == 0) {
    // 字段不存在，添加字段
    $sql = "ALTER TABLE report ADD COLUMN attachment VARCHAR(500) DEFAULT NULL";
    
    if (mysql_query($sql, $con)) {
        echo "成功添加attachment字段到report表！";
    } else {
        echo "添加字段失败: " . mysql_error();
    }
} else {
    echo "attachment字段已存在！";
}

mysql_close($con);
?>

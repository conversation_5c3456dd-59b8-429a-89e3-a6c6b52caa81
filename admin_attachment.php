<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
  <link rel="stylesheet" href="./index.css" />
  <title>报告附件管理</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    h1 {
      text-align: center;
      padding: 20px 0;
    }

    .box-card {
      max-width: 1000px;
      margin: 0 auto;
    }

    .btn {
      display: block;
      margin: 20px auto;
    }

    @media only screen and (max-width: 480px) {
      body {
        padding: 10px;
      }

      .el-col-12 {
        width: 100% !important;
      }
    }
  </style>
</head>

<body>
  <div id="app">
    <h1>报告附件管理</h1>
    <el-card class="box-card" style="margin-bottom: 20px">
      <el-form label-width="160px" :model="form" ref="form" label-suffix=" :">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告编号" prop="code" :rules="[
                  { required: true, message: '请输入报告编号'}
                ]">
              <el-input v-model="form.code" placeholder="请输入报告编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下载地址" prop="attachment" :rules="[
                  { required: true, message: '请输入下载地址'}
                ]">
              <el-input v-model="form.attachment" placeholder="请输入完整的下载地址"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="submitForm">添加附件</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
  <script src="./vue.js"></script>
  <script src="./index.js"></script>
  <script src="./axios.min.js"></script>
  <script>
    new Vue({
      el: '#app',
      data() {
        return {
          form: {
            code: '',// 报告编号
            attachment: '' // 附件地址
          }
        }
      },
      methods: {
        submitForm(formName) {
          this.$refs['form'].validate(valid => {
            if (valid) {
              axios({
                method: 'post',
                url: 'insert_attachment.php',
                data: this.form
              }).then(res => {
                if (res.data === '1' || res.data === 1) {
                  this.$message.success({ message: '附件添加成功！' })
                  this.resetForm()
                } else {
                  this.$message.error({ message: '添加失败，请检查报告编号是否正确' })
                }
              }).catch(err => {
                this.$message.error({ message: '添加失败，请稍后重试' })
              })
            } else {
              console.log('error submit!!')
              return false
            }
          })
        },
        resetForm() {
          this.$refs['form'].resetFields()
        }
      }
    })
  </script>
</body>

</html>

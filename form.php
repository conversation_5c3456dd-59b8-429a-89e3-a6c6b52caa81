<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
  <link rel="stylesheet" href="./index.css" />
  <title>信息录入</title>
  <style>
    * {
      margin: 0;
      padding: 0;
    }

    h1 {
      text-align: center;
      padding: 20px 0;
    }

    .box-card {
      max-width: 1000px;
      margin: 0 auto;
    }

    .btn {
      display: block;
      margin: 20px auto;
    }

    .el-input-number {
      width: 100%;
    }

    .el-date-editor {
      width: 100% !important;
    }

    @media only screen and (max-width: 480px) {
      body {
        padding: 10px;
      }

      .el-col-12 {
        width: 100% !important;
      }
    }
  </style>
  <title>Document</title>
</head>

<body>
  <div id="app">
    <h1>信息录入</h1>
    <el-card class="box-card">
      <el-form label-width="160px" :model="form" ref="form" label-suffix=" :">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告编号" prop="code" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input v-model="form.code" :maxlength="15" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="型号规格" prop="unit" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input v-model="form.unit" :maxlength="15" show-word-limit></el-input>
            </el-form-item>
            <el-form-item prop="item" label="检测项目" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input v-model="form.item" :maxlength="15" show-word-limit></el-input>
            </el-form-item>
            <el-form-item prop="address" label="检测地点" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input v-model="form.address" :maxlength="15" show-word-limit></el-input>
            </el-form-item>
            <el-form-item prop="temperature" label="环境条件-温度" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input-number :controls='false' v-model="form.temperature" :min="-1000" :max="1000" label="请输">
              </el-input-number>
            </el-form-item>
            <el-form-item prop="status" label="样品状态" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input v-model="form.status" :maxlength="30" show-word-limit></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样品名称/工程名称" prop="projectName" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input v-model="form.projectName" :maxlength="15" show-word-limit></el-input>
            </el-form-item>
            <el-form-item prop="company" label="委托单位" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input v-model="form.company" :maxlength="15" show-word-limit></el-input>
            </el-form-item>
            <el-form-item prop="checkingOfType" label="检测类型" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input v-model="form.checkingOfType" :maxlength="15" show-word-limit></el-input>
            </el-form-item>
            <el-form-item prop="date" label="检测日期" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-date-picker v-model="form.date" type="date" placeholder="选择日期" value-format='yyyy-MM-dd'>
              </el-date-picker>
            </el-form-item>
            <el-form-item prop="humidity" label="环境条件-湿度" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input-number :controls='false' v-model="form.humidity" :min="-1000" :max="1000" label="请输">
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="conclusion" label="检测结论" :rules="[
                  { required: true, message: '请输入'}
                ]">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入内容"
                :maxlength="300"
                show-word-limit
                v-model="form.conclusion">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="attachment" label="报告附件地址">
              <el-input
                v-model="form.attachment"
                placeholder="请输入报告下载地址（可选）"
                :maxlength="500"
                show-word-limit>
              </el-input>
              <div style="color: #909399; font-size: 12px; margin-top: 5px;">
                请输入完整的下载地址，如：http://example.com/report.pdf
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-button type="primary" class="btn" @click="submitForm">提交</el-button>
  </div>
  <script src="./vue.js"></script>
  <script src="./index.js"></script>
  <script src="./axios.min.js"></script>
  <script>
    new Vue({
      el: '#app',
      data() {
        return {
          info: {},
          form: {
            code: '',// 报告编号
            projectName: '',// 样品名称/工程名称
            unit: '',// 型号规格
            company: '',// 委托单位
            item: '',// 检测项目
            checkingOfType: '',// 检测类型
            address: '',// 检测地点
            date: '',// 检测日期
            temperature: undefined,// 环境条件- 温度
            humidity: undefined,//环境条件- 湿度
            status: '',//样品状态
            conclusion: '',//检测结论
            attachment: '',//报告附件地址
          }
        }
      },
      methods: {
        submitForm(formName) {
          console.log(this.form);
          this.$refs['form'].validate(valid => {
            if (valid) {
              axios({
                method: 'post',
                url: 'insert.php',
                data: this.form
              }).then(res => {
                if (res.status === 200) {
                  this.$message.success({ message: '提交成功' })
                } else {
                  this.$message.warning({ message: '提交失败' })
                }
              })
            } else {
              return false
            }
          })
        }
      }
    })
  </script>
</body>

</html>